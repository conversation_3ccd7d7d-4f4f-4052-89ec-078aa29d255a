package com.drxin.bizz.utils;

import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.StringUtils;
import com.drxin.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户层级关系工具类
 * 
 * 提供用户下级查找的通用方法，统一处理用户层级关系
 * 
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Component
public class UserHierarchyUtils {

    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 递归查找所有下级用户，遇到disciple时停止搜索
     * 
     * @param userId 起始用户ID
     * @return 所有下级用户列表（不包含disciple用户）
     */
    public List<SysUser> findAllSubordinates(String userId) {
        List<SysUser> allSubordinates = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        
        findAllSubordinatesRecursive(userId, allSubordinates, visited);
        
        log.debug("查找用户下级完成: userId={}, 下级数量={}", userId, allSubordinates.size());
        return allSubordinates;
    }

    /**
     * 递归查找所有下级用户，遇到disciple时停止搜索
     * 
     * @param userId 用户ID
     * @param allSubordinates 所有下级用户集合
     * @param visited 已访问的用户ID集合，防止循环引用
     */
    private void findAllSubordinatesRecursive(String userId, List<SysUser> allSubordinates, Set<String> visited) {
        if (StringUtils.isEmpty(userId) || visited.contains(userId)) {
            return;
        }
        
        visited.add(userId);
        
        try {
            // 查询当前用户的直接下级
            List<SysUser> subordinates = sysUserMapper.selectUsersByInviterId(userId);
            
            for (SysUser subordinate : subordinates) {
                String subordinateId = subordinate.getUserId().toString();
                if (!visited.contains(subordinateId)) {
                    // 检查用户类型是否包含disciple
                    if (containsDisciple(subordinate.getUserType())) {
                        // 如果包含disciple，不加入列表，也不继续搜索其下级
                        log.debug("用户包含disciple身份，停止搜索: userId={}, userType={}", 
                            subordinate.getUserId(), subordinate.getUserType());
                        continue;
                    }
                    
                    // 不包含disciple，加入下级用户列表
                    allSubordinates.add(subordinate);
                    
                    // 继续递归查找下级的下级
                    findAllSubordinatesRecursive(subordinateId, allSubordinates, visited);
                }
            }
        } catch (Exception e) {
            log.error("查找用户下级失败: userId={}", userId, e);
        }
    }

    /**
     * 检查用户类型是否包含disciple
     * 
     * @param userType 用户类型字符串
     * @return 是否包含disciple
     */
    public boolean containsDisciple(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return false;
        }
        
        // userType是逗号分隔的字符串，检查是否包含disciple
        String[] userTypes = userType.split(",");
        for (String type : userTypes) {
            if ("disciple".equals(type.trim())) {
                return true;
            }
        }
        
        return false;
    }
}
