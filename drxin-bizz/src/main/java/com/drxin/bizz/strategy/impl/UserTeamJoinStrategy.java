package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.domain.UserApply;
import com.drxin.bizz.mapper.TeamMemberMapper;
import com.drxin.bizz.mapper.UserApplyMapper;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 用户团队加入策略
 * 
 * 当用户身份申请通过时，自动将用户加入上级的团队
 * 如果没有上级或上级没有团队，则设置团队ID为-1
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class UserTeamJoinStrategy implements ContributionStrategy {

    @Autowired
    private ITeamMemberService teamMemberService;

    @Resource
    private TeamMemberMapper teamMemberMapper;

    @Resource
    private UserApplyMapper userApplyMapper;

    @Override
    public String getSupportAction() {
        return "USER_TEAM_JOIN";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            Map<String, Object> context = event.getContext();
            @SuppressWarnings("unchecked")
            List<Long> successIds = (List<Long>) context.get("successIds");

            if (successIds == null || successIds.isEmpty()) {
                log.warn("成功申请ID列表为空，跳过团队加入处理");
                return;
            }

            log.info("开始处理用户自动加入团队: 申请数量={}", successIds.size());

            int successCount = 0;
            for (Long applyId : successIds) {
                try {
                    if (processUserTeamJoin(applyId)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理申请ID[{}]团队加入失败", applyId, e);
                }
            }

            log.info("用户自动加入团队处理完成: 总数={}, 成功={}", successIds.size(), successCount);

        } catch (Exception e) {
            log.error("处理用户团队加入失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            throw e; // 重新抛出异常，让监听器记录失败状态
        }
    }

    /**
     * 处理单个用户的团队加入
     * 
     * @param applyId 申请ID
     * @return 是否处理成功
     */
    private boolean processUserTeamJoin(Long applyId) {
        try {
            // 查询申请信息
            UserApply userApply = userApplyMapper.selectUserApplyById(applyId);
            if (userApply == null || userApply.getUserId() == null) {
                log.warn("申请信息不存在或用户ID为空: applyId={}", applyId);
                return false;
            }

            String userId = userApply.getUserId().toString();
            String teamId = "-1"; // 默认团队ID

            // 如果有上级，查询上级的团队
            if (userApply.getInviterId() != null) {
                String inviterTeamId = teamMemberMapper.selectTeamIdByMemberId(userApply.getInviterId().toString());
                if (inviterTeamId != null && !inviterTeamId.trim().isEmpty()) {
                    teamId = inviterTeamId;
                    log.info("找到上级团队: userId={}, inviterId={}, teamId={}", 
                        userId, userApply.getInviterId(), teamId);
                } else {
                    log.info("上级没有团队，使用默认团队: userId={}, inviterId={}", 
                        userId, userApply.getInviterId());
                }
            } else {
                log.info("没有上级，使用默认团队: userId={}", userId);
            }

            // 检查用户是否已经在团队中
            String existingTeamId = teamMemberMapper.selectTeamIdByMemberId(userId);
            if (existingTeamId != null && !existingTeamId.trim().isEmpty()) {
                // 用户已在团队中，更新用户的memberType和upgradedTime
                log.info("用户已在团队中，更新成员信息: userId={}, existingTeamId={}", userId, existingTeamId);

                TeamMember updateTeamMember = new TeamMember();
                updateTeamMember.setMemberId(userId);
                updateTeamMember.setMemberType(userApply.getUserType());
                updateTeamMember.setUpgradedTime(DateUtils.getNowDate());

                int updateResult = teamMemberService.updateTeamMember(updateTeamMember);
                if (updateResult > 0) {
                    log.info("用户团队成员信息更新成功: userId={}, memberType={}",
                        userId, updateTeamMember.getMemberType());
                    return true;
                } else {
                    log.error("用户团队成员信息更新失败: userId={}", userId);
                    return false;
                }
            }

            // 用户不在团队中，创建新的团队成员记录
            TeamMember teamMember = new TeamMember();
            teamMember.setMemberId(userId);
            teamMember.setTeamId(teamId);
            teamMember.setMemberName(StringUtils.isNotEmpty(userApply.getRealName()) ? 
                userApply.getRealName() : "用户" + userId);
            teamMember.setMemberType(userApply.getUserType());
            teamMember.setUpgradedTime(DateUtils.getNowDate());

            int result = teamMemberService.insertTeamMember(teamMember);
            if (result > 0) {
                log.info("用户成功加入团队: userId={}, teamId={}, memberType={}", 
                    userId, teamId, teamMember.getMemberType());
                return true;
            } else {
                log.error("用户加入团队失败: userId={}, teamId={}", userId, teamId);
                return false;
            }

        } catch (Exception e) {
            log.error("处理用户团队加入异常: applyId={}", applyId, e);
            return false;
        }
    }
}
