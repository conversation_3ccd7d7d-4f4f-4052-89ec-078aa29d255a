package com.drxin.bizz.strategy.impl;

import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.UserHierarchyUtils;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.ContributionActionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 团队成员自动添加策略
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Component
public class TeamMemberAutoAddStrategy implements ContributionStrategy {

    @Autowired
    private ITeamMemberService teamMemberService;

    @Autowired
    private UserHierarchyUtils userHierarchyUtils;

    @Override
    public String getSupportAction() {
        return "TEAM_MEMBER_AUTO_ADD";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            Map<String, Object> context = event.getContext();
            String teamId = (String) context.get("teamId");
            String leaderId = (String) context.get("leaderId");
            String teamName = (String) context.get("teamName");

            log.info("开始处理团队成员自动添加: teamId={}, leaderId={}, teamName={}", 
                teamId, leaderId, teamName);

            // 获取所有需要加入团队的用户
            List<SysUser> allMembers = userHierarchyUtils.findAllSubordinates(leaderId);
            
            // 批量创建团队成员
            batchCreateTeamMembers(teamId, allMembers);
            
            log.info("团队成员自动添加完成: teamId={}, 添加成员数量={}", 
                teamId, allMembers.size());

        } catch (Exception e) {
            log.error("处理团队成员自动添加失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            throw e; // 重新抛出异常，让监听器记录失败状态
        }
    }



    /**
     * 批量创建团队成员
     * 
     * @param teamId 团队ID
     * @param users 用户列表
     */
    private void batchCreateTeamMembers(String teamId, List<SysUser> users) {
        if (users.isEmpty()) {
            log.info("没有需要添加的团队成员: teamId={}", teamId);
            return;
        }
        
        List<TeamMember> teamMembers = new ArrayList<>();
        
        for (SysUser user : users) {
            TeamMember teamMember = new TeamMember();
            teamMember.setMemberId(user.getUserId().toString());
            teamMember.setTeamId(teamId);
            teamMember.setMemberName(StringUtils.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getNickName());
            teamMember.setMemberType(mapUserTypeToMemberType(user.getUserType()));
            teamMember.setUpgradedTime(user.getUpgradedTime());
            
            teamMembers.add(teamMember);
        }
        
        // 批量插入团队成员
        int successCount = 0;
        for (TeamMember teamMember : teamMembers) {
            try {
                teamMemberService.insertTeamMember(teamMember);
                successCount++;
            } catch (Exception e) {
                log.error("插入团队成员失败: memberId={}, teamId={}", 
                    teamMember.getMemberId(), teamMember.getTeamId(), e);
            }
        }
        
        log.info("批量创建团队成员完成: teamId={}, 总数={}, 成功={}", 
            teamId, teamMembers.size(), successCount);
    }

    /**
     * 将用户类型映射为团队成员类型
     * 
     * @param userType 用户类型
     * @return 团队成员类型
     */
    private String mapUserTypeToMemberType(String userType) {
        if (StringUtils.isEmpty(userType)) {
            return "普通成员";
        }
        
        // userType是逗号分隔的字符串，取第一个有效的类型
        String[] userTypes = userType.split(",");
        for (String type : userTypes) {
            String trimmedType = type.trim();
            switch (trimmedType) {
                case "aider":
                    return "急救员";
                case "mentor":
                    return "急救导师";
                case "disciple":
                    return "弟子";
                case "general":
                default:
                    return "普通成员";
            }
        }
        
        return "普通成员";
    }
}
