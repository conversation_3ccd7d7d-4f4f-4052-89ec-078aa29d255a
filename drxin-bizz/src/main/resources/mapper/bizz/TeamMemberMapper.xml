<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.TeamMemberMapper">
    
    <resultMap type="com.drxin.bizz.domain.TeamMember" id="TeamMemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="memberType"    column="member_type"    />
        <result property="upgradedTime"    column="upgraded_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTeamMemberVo">
        select member_id, team_id, member_name, member_type, upgraded_time, create_by, create_time, update_by, update_time from team_member
    </sql>

    <select id="selectTeamMemberList" parameterType="com.drxin.bizz.domain.TeamMember" resultMap="TeamMemberResult">
        <include refid="selectTeamMemberVo"/>
        <where>  
            <if test="teamId != null  and teamId != ''"> and team_id = #{teamId}</if>
            <if test="memberName != null  and memberName != ''"> and member_name like concat('%', #{memberName}, '%')</if>
            <if test="memberType != null  and memberType != ''"> and member_type = #{memberType}</if>
            <if test="upgradedTime != null "> and upgraded_time = #{upgradedTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeamMemberById" parameterType="String" resultMap="TeamMemberResult">
        <include refid="selectTeamMemberVo"/>
        where member_id = #{memberId}
    </select>
        
    <insert id="insertTeamMember" parameterType="com.drxin.bizz.domain.TeamMember">
        insert into team_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="memberName != null">member_name,</if>
            <if test="memberType != null">member_type,</if>
            <if test="upgradedTime != null">upgraded_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="memberName != null">#{memberName},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="upgradedTime != null">#{upgradedTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeamMember" parameterType="com.drxin.bizz.domain.TeamMember">
        update team_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="memberName != null">member_name = #{memberName},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="upgradedTime != null">upgraded_time = #{upgradedTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteTeamMemberById" parameterType="String">
        delete from team_member where member_id = #{memberId}
    </delete>

    <delete id="deleteTeamMemberByIds" parameterType="String">
        delete from team_member where member_id in
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>

    <select id="selectTeamIdByMemberId" parameterType="String" resultType="String">
        select team_id from team_member where member_id = #{memberId} limit 1
    </select>
</mapper>
